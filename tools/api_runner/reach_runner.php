<?php

/**
 * This script reads in a CSV list of records, sends them to the REACH API, and evaluates the results.
 *
 * Run: php reach_runner.php
 */

const BASE_URL_REACH_PROD = 'https://api.versium.com/v2';
const BASE_URL_REACH_STG = 'https://api-stg.versium.com/v2';
const INPUT_LIST_PATH = './lists/';
const REPORT_DIR = './reports/';

const CUSTOM_HEADER_MAP = [
    'first'    => 'first_name',
    'last'     => 'last_name',
    'address'  => 'Mailing Address',
    'zip'      => 'Mailing Zip Code',
    'phone'    => 'Verified_number',
];

const LISTS = [
    'TS_Skipforce_Phones.csv' => [
        'headerMap' => [
            'first'    => 'FirstName',
            'last'     => 'LastName',
            'address'  => 'Mailing Address',
            'city'     => 'Mailing City',
            'state'    => 'Mailing State',
            'zip'      => 'Mailing Zip',
            'phone'    => 'Phone',
      ],
    ],
    'SynergyTestIDISource.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
    'SynergyTestVersiumSource.csv' => [
        'headerMap' => CUSTOM_HEADER_MAP,
    ],
];

enum Environment {
    case PROD;
    case STG;
}

/**
 * @param string $list - The name of the list to read in
 * @param array $inputParams - The fields in the list to send as request params
 * @param string $operationUrl - Reach endpoint to hit
 * @param Environment $env - staging || production
 * @param bool $writeHeader - Write header row to the report
 */
function runTest(
    string $list,
    array $inputParams,
    string $operationUrl,
    Environment $env,
    $writeHeader = true,
)
{
    $counter = [
        'matches' => 0,
        'first_phone_correct' => 0,
        'alt_phone_correct' => 0,
        'no_correct_phones' => 0,
        'total_phones' => 0,
        'total_correct_phones' => 0,

        'curl_err_count' => 0,
        'curl_errs' => [],
        'reach_err_count' => 0,
        'reach_errs' => [],
        'optout_err_count' => 0,
        'optout_errs' => [],

        'example_url' => '',
        'recs_processed' => 0,
    ];
    $urlExample = null;
    $startTime = microtime(true);

    # curl_multi
    $mh = curl_multi_init();
    $handles = [];
    $batchSize = 10;

    # read in $inputList
    $fp = fopen(INPUT_LIST_PATH . $list, 'r');
    $header = fgetcsv($fp);

    while (($rec = fgetcsv($fp)) !== false) {
        # gather params based on selected inputs
        $inputRec = array_combine($header, $rec);
        $params = getParams($inputRec, $inputParams, $list);
        $reqUrl = createReqUrl($operationUrl, $params, $env);

        if ($urlExample == null) {
            $urlExample = $reqUrl;
            $counter['example_url'] = $urlExample;
        }

        # init curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        setReachHeaders($env, $ch);

        curl_multi_add_handle($mh, $ch);
        $handles[] = [$ch, $inputRec, $reqUrl];

        # send batch
        if (count($handles) === $batchSize) {
            $responses = sendBatchRequest($mh, $handles, $counter, $env);

            # log progress
            $counter['recs_processed'] += count($responses);
            if ($counter['recs_processed'] % 250 === 0) {
                echo "Processed $counter[recs_processed] records \n";
            }

            foreach($responses as $response) {
                processResponse($response, $counter);
            }
        }

    }
    fclose($fp);

    # send any remaining requests
    if (count($handles) > 0) {
        $responses = sendBatchRequest($mh, $handles, $counter, $env);

        foreach($responses as $response) {
            processResponse($response, $counter);
        }
        $counter['recs_processed'] += count($responses);
    }

    var_dump($counter);
    writeToCSV($writeHeader, $list, $counter, $operationUrl, $urlExample);
}

function setReachHeaders(Environment $env, $handle) {
    if ($env === Environment::PROD) {
        curl_setopt($handle, CURLOPT_HTTPHEADER, ['X-Versium-Api-Key: ' . '67049e4a-4d4e-4ad6-b0bd-ef69d3945087']);
    } else {
        curl_setopt($handle, CURLOPT_HTTPHEADER, ['X-Versium-Api-Key: ' . '2dfdf519-77e0-4c0c-9342-bc238c2317ae']);
    }
}

function getParams(array $inputRec, array $inputParams, $list): array
{
    $params = [];
    $headerMap = LISTS[$list]['headerMap'];

    foreach($inputParams as $param) {
        if (isset($headerMap[$param])) {
            $params[$param] = $inputRec[$headerMap[$param]];
        }
    }

    return $params;
}

function createReqUrl(
    string $operationUrl,
    array $params = [],
    Environment $env = Environment::PROD
    ): string
{
    $auxParams = [
        'cacheBuster' => rand(),
        // 'prodids' => 'crawlbeewp,Cell5,durtwp',
        // 'rcfg_all_fields' => 1,
    ];
    $finalParams = array_merge($params, $auxParams);
    $baseUrl = match($env) {
        Environment::PROD => BASE_URL_REACH_PROD,
        Environment::STG  => BASE_URL_REACH_STG,
    };

    return $baseUrl . $operationUrl . http_build_query($finalParams);
}

function sendBatchRequest($multiHandle, &$handles, &$counter, $env): array
{
    $responses = [];

    # execute multi curl
    $running = null;
    do {
      curl_multi_exec($multiHandle, $running);
      usleep(10000);
    } while ($running);

    # process the responses
    foreach ($handles as [$ch, $inputRec, $reqUrl]) {
        $response = curl_multi_getcontent($ch);
        $errNum = curl_errno($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        # retry
        if (in_array($status, [429, 500])) {
            echo "Got status: $status. ErrNum: $errNum. Retrying...\n";
            usleep(50000);

            $nch = curl_init();
            curl_setopt($nch, CURLOPT_URL, $reqUrl);
            curl_setopt($nch, CURLOPT_RETURNTRANSFER, 1);
            setReachHeaders($env, $nch);

            $res = curl_exec($nch);
            $status = curl_getinfo($nch, CURLINFO_HTTP_CODE);
            echo "new status: $status. New Response: \n";
            // var_dump($res);
        }

        # curl errors
        if ($errNum || $status != 200) {
            $counter['curl_err_count'] += 1;
            array_push($counter['curl_errs'], 'status: ' . $status . ' url: ' . $reqUrl);
        }
        # reach errors
        $jsonRes = json_decode($response, false, 10);
        if (isset($jsonRes->versium->errors)) {
            $counter['reach_err_count'] += 1;
            array_push($counter['reach_errs'], $jsonRes->versium->errors[0]);
        }


        # store the response
        $responses[] = [$jsonRes, $inputRec, $reqUrl];

        curl_multi_remove_handle($multiHandle, $ch);
    }
    $handles = [];

    return $responses;
}

function processResponse($response, array &$counter)
{
    [$decodedResponse, $inputRec, $reqUrl] = $response;
    // var_dump($decodedResponse);
    // var_dump($inputRec);
    // var_dump($reqUrl);
    $inputPhone = $inputRec['Verified_number'];


    # did we bring back match?
    if (!empty($decodedResponse->versium->results)) {
        $result = $decodedResponse->versium->results[0];
        $counter['matches'] += 1;
        $phones = [];

        # is the first phone correct?
        if ($result->Phone == $inputPhone) {
            $counter['first_phone_correct'] += 1;
            $counter['total_correct_phones'] += 1;

        } else {
            # are any of the alt phones correct?
            $keys = ['Alt Phone 1', 'Alt Phone 2', 'Alt Phone 3', 'Alt Phone 4', 'Alt Phone 5'];

            foreach ($keys as $key) {
                if (isset($result->{$key})) {
                    array_push($phones, $result->{$key});
                }
            }

            $correct = in_array($inputPhone, $phones);
            if ($correct) {
                $counter['alt_phone_correct'] += 1;
                $counter['total_correct_phones'] += 1;
            } else {
                $counter['no_correct_phones'] += 1;
            }
        }
        # count total phones returned
        if (isset($result->Phone)) {
            array_push($phones, $result->Phone);
        }
        $counter['total_phones'] += count($phones);
    }
}

function writeToCSV(
    bool $withHeader,
    string $list,
    array $counter,
    string $operationUrl,
    string $urlExample,
    ) {
    $reportName = date('Y-m-d') . '_Result_' . '.csv';

    $fp = fopen(REPORT_DIR . $reportName, 'a');
    $header = ['TruthSet', 'Operation', 'Matches', 'Match %', 'First Phone Correct', 'First Phone %', 'Alt Phone Correct', 'Alt Phone %', 'No Correct Phones', 'No Correct %', 'Total Phones', 'Total Correct Phones'];
    if ($withHeader) {
        fputcsv($fp, $header);
    }
    fputcsv($fp, [
        $list,
        $operationUrl,
        $counter['matches'],
        "",
        $counter['first_phone_correct'],
        "",
        $counter['alt_phone_correct'],
        "",
        $counter['no_correct_phones'],
        "",
        $counter['total_phones'],
        $counter['total_correct_phones'],
    ]);

    fclose($fp);
}

function time2string($time) {
    $d = floor($time/86400);
    $_d = ($d < 10 ? '0' : '').$d;

    $h = floor(($time-$d*86400)/3600);
    $_h = ($h < 10 ? '0' : '').$h;

    $m = floor(($time-($d*86400+$h*3600))/60);
    $_m = ($m < 10 ? '0' : '').$m;

    $s = $time-($d*86400+$h*3600+$m*60);
    $_s = ($s < 10 ? '0' : '').$s;

    $time_str = $_d.':'.$_h.':'.$_m.':'.$_s;

    return $time_str;
}


$tests = [
    [
        'list'         => 'SynergyTestIDISource.csv',
        'inputParams'  => ['first', 'last', 'address', 'zip'],
        'operationUrl' => '/contact?output[]=phone_multiple&',
        'env'          => Environment::PROD,
        'writeHeader'  => true,
    ],
    // [
    //     'list'         => 'SynergyTestVersiumSource.csv',
    //     'inputParams'  => ['first', 'last', 'address', 'zip'],
    //     'operationUrl' => '/contact?output[]=phone_multiple&',
    //     'env'          => Environment::PROD,
    //     'writeHeader'  => false,
    // ],
    // [
    //     'list'         => 'TS_Skipforce_Phones.csv',
    //     'inputParams'  => ['first', 'last', 'address', 'city', 'state', 'zip'],
    //     'operationUrl' => '/contact?output[]=phone_multiple&',
    //     'env'          => Environment::PROD,
    //     'writeHeader'  => true,
    // ],
];

foreach($tests as $test) {
    runTest(
        $test['list'],
        $test['inputParams'],
        $test['operationUrl'],
        $test['env'],
        $test['writeHeader']
    );
}