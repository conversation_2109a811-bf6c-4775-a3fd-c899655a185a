#!/bin/bash

# <PERSON>ript to move all CSV files from reports/ to reports/previous/
# Usage: ./move_csv_to_previous.sh

# Set script directory as base (assuming we're in compliance_testing dir)
REPORTS_DIR="/home/<USER>/tbull/compliance_testing/reports"
PREVIOUS_DIR="/home/<USER>/tbull/compliance_testing/reports/previous"

# Check if reports directory exists
if [ ! -d "$REPORTS_DIR" ]; then
    echo "Error: Reports directory '$REPORTS_DIR' does not exist!"
    exit 1
fi

# Create previous directory if it doesn't exist
if [ ! -d "$PREVIOUS_DIR" ]; then
    echo "Creating directory: $PREVIOUS_DIR"
    mkdir -p "$PREVIOUS_DIR"
fi

# Count CSV files in reports directory
csv_count=$(find "$REPORTS_DIR" -maxdepth 1 -name "*.csv" -type f | wc -l)

if [ "$csv_count" -eq 0 ]; then
    echo "No CSV files found in $REPORTS_DIR directory."
    exit 0
fi

echo "Found $csv_count CSV file(s) in $REPORTS_DIR directory."
echo "Moving CSV files to $PREVIOUS_DIR..."

# Move all CSV files from reports to reports/previous
moved_count=0
for csv_file in "$REPORTS_DIR"/*.csv; do
    # Check if the glob pattern matched any files
    if [ -f "$csv_file" ]; then
        filename=$(basename "$csv_file")
        echo "Moving: $filename"

        # If file already exists in previous directory, add timestamp
        if [ -f "$PREVIOUS_DIR/$filename" ]; then
            timestamp=$(date +"%Y%m%d_%H%M%S")
            new_name="${filename%.*}_${timestamp}.${filename##*.}"
            echo "  File already exists, renaming to: $new_name"
            mv "$csv_file" "$PREVIOUS_DIR/$new_name"
        else
            mv "$csv_file" "$PREVIOUS_DIR/"
        fi

        ((moved_count++))
    fi
done

echo "Successfully moved $moved_count CSV file(s) to $PREVIOUS_DIR"
