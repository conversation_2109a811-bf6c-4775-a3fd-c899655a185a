<?php

include_once 'column_validator_ruleset.php';
include_once 'column_validator_helper.php';


# read in the Converse sample data
$fileName = "cv_slice_listgen_1000_1_5000.csv";
$path = "./samples/$fileName";
$fp = fopen($path, 'r');
$headerRow = fgetcsv($fp);


$failures = [];
$rowNumber = 1; # row 0 is the header

while (($rec = fgetcsv($fp)) !== false) {

    $fullRecord = array_combine($headerRow, $rec);

    # iterate through column headers
    foreach ($headerRow as $columnHeader) {

        # if a test exists for the column, perform it
        if (array_key_exists($columnHeader, $columnValueTests)) {
            $test = $columnValueTests[$columnHeader];
            $value = $fullRecord[$columnHeader];

            if ($test['canBeEmpty'] && empty($value)) {
                continue;
            }

            if ($test['pattern'] === null) {
                continue;
            }

            if ($test['canBeEmpty'] === false && $value === '') {
                echo "Failed value test for $columnHeader; An Empty value was found. Row: $rowNumber \n";
                logFailure($failures, $columnHeader, $value, $test, $rowNumber, ErrorType::EMPTY_VALUE);
                continue;
            }

            switch (gettype($test['pattern'])) {
                case 'string':
                    if (!preg_match($test['pattern'], $value)) {
                        echo "Failed value test for $columnHeader with value $value. Row: $rowNumber \n";
                        logFailure($failures, $columnHeader, $value, $test, $rowNumber, ErrorType::PATTERN_MISMATCH);
                    }
                    break;
                case 'array':
                    if (!in_array($value, $test['pattern'])) {
                        echo "Failed value test for $columnHeader with value $value. Row: $rowNumber \n";
                        logFailure($failures, $columnHeader, $value, $test, $rowNumber, ErrorType::PATTERN_MISMATCH);
                    }
                    break;
                default:
            }
        }
    }

    $rowNumber++;
}

file_put_contents('validation_failures.json', json_encode($failures, JSON_PRETTY_PRINT));

echo "Tests complete.\n";