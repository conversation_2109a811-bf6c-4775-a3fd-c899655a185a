<?php

// open ssh tunnel to db server & forward sql port before running!
//
// Usage: php sql_sampler.php [converse] [modulus] [targetBucket] [sampleSize] [getSchema]
// Arguments:
//   converse     - version of converse: append or listgen
//   modulus      - Modulus value for sampling (default: 1000)
//   targetBucket - Target bucket for sampling (default: 1)
//   sampleSize   - Maximum number of rows to return (default: 5000)
//   getSchema    - Whether to get the schema (0 or 1, default: 0)
//
// Example: php sql_sampler.php listgen 1000 1 5000 1


# load env
require_once __DIR__ . '/../../vendor/autoload.php';
use Symfony\Component\Dotenv\Dotenv;
$dotenv = new Dotenv();
$dotenv->load(__DIR__ . '/../../.env');

# Parse command line arguments
$database     = isset($argv[1]) ? $argv[1] : 'append';
$modulus      = isset($argv[2]) ? (int)$argv[2] : 1000;
$targetBucket = isset($argv[3]) ? (int)$argv[3] : 1;
$sampleSize   = isset($argv[4]) ? (int)$argv[4] : 5000;
$getSchema    = isset($argv[5]) ? (bool)$argv[5] : false;

# Validate arguments
if ($database !== 'append' && $database !== 'listgen') {
    echo "Error: Invalid database. Must be 'append' or 'listgen'\n";
    exit(1);
}
if ($modulus <= 0) {
    echo "Error: modulus must be greater than 0\n";
    exit(1);
}
if ($targetBucket < 0 || $targetBucket >= $modulus) {
    echo "Error: targetBucket must be between 0 and " . ($modulus - 1) . "\n";
    exit(1);
}
if ($sampleSize <= 0) {
    echo "Error: sampleSize must be greater than 0\n";
    exit(1);
}

echo "SQL Sampler: Sampling from $database Converse. Using parameters: modulus=$modulus, targetBucket=$targetBucket, sampleSize=$sampleSize\n";


# database access
$host = '127.0.0.1';
$port = '3307';
$dbname = match($database) {
    'append' => $_ENV['DB_NAME_CV_APPEND'],
    'listgen' => $_ENV['DB_NAME_CV_LISTGEN'],
};
$user = $_ENV['DB_USER'];
$password = $_ENV['DB_PASSWORD'];

# output
$fileName = "cv_slice_$database" . "_$modulus" . "_$targetBucket" . "_$sampleSize.csv";
$outputDir = __DIR__ . '/samples/';
$outputFile = $outputDir . $fileName;

# attempt db connection
try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "SQL Sampler: Error connecting to database: " . $e->getMessage();
    exit;
}

# get schema
if ($getSchema) {
    try {
        echo "SQL Sampler: Attempting to get schema...\n";
        $schemaQuery = "DESCRIBE $dbname.Converse";
        $stmt = $pdo->query($schemaQuery);
        $schema = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $path = $outputDir . "cv_$database" . "_schema.json";

        file_put_contents(
            $path,
            json_encode($schema, JSON_PRETTY_PRINT)
        );
        echo "SQL Sampler: Schema successfully written to $path\n";
    } catch (PDOException $e) {
        echo "SQL Sampler: Error getting schema: " . $e->getMessage();
        exit;
    }
}


# get sample
try {
    $sql = "
        SELECT *
        FROM Converse
        WHERE MOD(CONV(SUBSTRING(MD5(`STHHLD`), 1, 8), 16, 10), $modulus) = $targetBucket
        LIMIT $sampleSize
    ";

    echo "SQL Sampler: Executing query: $sql\n";

    # Execute query
    $start = microtime(true);
    $stmt = $pdo->query($sql);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($rows)) {
        echo "No rows returned.\n";
        exit;
    }

    $end = microtime(true);
    $time = $end - $start;
    echo "SQL Sampler: SQL Query Completed in $time seconds.\n";

    # write to CSV
    $fp = fopen($outputFile, 'w');
    fputcsv($fp, array_keys($rows[0]));
    foreach ($rows as $row) {
        fputcsv($fp, $row);
    }
    fclose($fp);

    echo "SQL Sampler: Sample exported to $outputFile\n";

} catch (PDOException $e) {
    echo "SQL Sampler: Database error: " . $e->getMessage();
    exit;
}
