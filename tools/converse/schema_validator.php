<?php

include_once 'column_validator_ruleset.php';

# Parse command line arguments
$database = isset($argv[1]) ? $argv[1] : 'append';

# Validate arguments
if ($database !== 'append' && $database !== 'listgen') {
    echo "Error: Invalid database. Must be 'append' or 'listgen'\n";
    exit(1);
}

# read in the schema json
$schemaPath = "./samples/2025_09_10_$database/cv_$database" . "_schema.json";
$json = file_get_contents($schemaPath);
$schema = json_decode($json, true);

foreach ($schema as $i => $column) {
    $columnName = $column['Field'];
    $columnType = $column['Type'];
    $rulesetType = $columnValueTests[$columnName]['type'];

    # check if column is in the ruleset
    if (!array_key_exists($columnName, $columnValueTests)) {
        echo "Error: Column $columnName is not in the ruleset\n";
        exit(1);
    }

    # check type
    if ($rulesetType !== $columnType) {
        echo "Error: Column $columnName has type $columnType in the schema, but $rulesetType in the ruleset\n";
        exit(1);
    }

    # check column position
    if ($columnValueTests[$columnName]['position'] !== $i) {
        echo "Error: Column $columnName is in the wrong position. Expected " . $columnValueTests[$columnName]['position'] . " but found $i\n";
        exit(1);
    }
}

# check for missing columns
foreach ($columnValueTests as $columnName => $test) {
    if (!array_key_exists($columnName, $schema)) {
        echo "Error: Column $columnName is in the ruleset but not in the schema\n";
        exit(1);
    }
}

echo "Schema validation complete. No errors found.\n";