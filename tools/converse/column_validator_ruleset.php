<?php

include_once 'column_validator_helper.php';

//TODO: search for entries that have pattern => null.

$columnValueTests = [
    "SKID" => [
        'pattern' => '/^\d{5}_\d{3}$/',
        'canBeEmpty' => false,
        'type' => 'varchar(128)',
        'position' => 0,
    ],
    "ADDRID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
        'type' => 'bigint(20)',
        'position' => 1,
    ],
    "STADDR" => [
        'pattern' => '/^\d{5}:::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(128)',
        'position' => 2,
    ],
    "HHLDID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
        'type' => 'bigint(20)',
        'position' => 3,
    ],
    "STHHLD" => [
        'pattern' => '/^\d{5}:[A-Za-z]+::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(128)',
        'position' => 4,
    ],
    "INDIVID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
        'type' => 'bigint(20)',
        'position' => 5,
    ],
    "ST10" => [
        'pattern' => '/^\d{5}:[A-Za-z]+:[A-Za-z]+:[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(128)',
        'position' => 6,
    ],
    "FirstName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(32)',
        'position' => 7,
    ],
    "MiddleName" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 8,
    ],
    "LastName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(32)',
        'position' => 9,
    ],
    "Address" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(128)',
        'position' => 10,
    ],
    "Unit" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 11,
    ],
    "City" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 12,
    ],
    "State" => [
        'pattern' => '/^[A-Z]{2}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(2)',
        'position' => 13,
    ],
    "Zip" => [
        'pattern' => '/^\d{4,8}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 14,
    ],
    "Zip4" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(10)',
        'position' => 15,
    ],
    "AddrTypeIndicator" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 16,
    ],
    "CensusMedianHomeValue" => [
        'pattern' => '/^\d{1,5}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 17,
    ],
    "CensusMedianHouseholdIncome" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 18,
    ],
    "NumSrc" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 19,
    ],
    "FileDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => false,
        'type' => 'varchar(8)',
        'position' => 20,
    ],
    "BaseRecVerificationDate" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 21,
    ],
    "DOB" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 22,
    ],
    "Phone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(10)',
        'position' => 23,
    ],
    "GenderCode" => [
        'pattern' => '/^[MF]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 24,
    ],
    "InferredHouseholdRank" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 25,
    ],
    "EstimatedHouseholdIncome" => [
        'pattern' => '/^[A-S]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 26,
    ],
    "NetWorth" => [
        'pattern' => '/^[A-I]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 27,
    ],
    "NumberCreditLines" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 28,
    ],
    "RangeOfNewCredit" => [
        'pattern' => '/^[1-7]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 29,
    ],
    "Education" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 30,
    ],
    "Occupation" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => false,
        'type' => 'varchar(1)',
        'position' => 31,
    ],
    "OccupationDetail" => [
        'pattern' => '/^[A-Z0-9]{4}$/',
        'canBeEmpty' => false,
        'type' => 'varchar(4)',
        'position' => 32,
    ],
    "BusinessOwner" => [
        'pattern' => '/^[1-9,A]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 33,
    ],
    "NumberOfChildren" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 34,
    ],
    "PresenceOfChildren" => [
        'pattern' => '/^[Y,N]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 35,
    ],
    "MaritalStatusInHousehold" => [
        'pattern' => '/^[SMAB]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 36,
    ],
    "HomeOwnerRenter" => [
        'pattern' => '/^[H,R,9]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 37,
    ],
    "LengthOfResidence" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 38,
    ],
    "DwellingType" => [
        'pattern' => '/^[S,M]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 39,
    ],
    "NumberOfAdults" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 40,
    ],
    "HouseholdSize" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 41,
    ],
    "HomeMarketValue" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 42,
    ],
    "GenerationsInHousehold" => [
        'pattern' => '/^[1-4]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 43,
    ],
    "MailOrderBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 44,
    ],
    "MailerOrderResponder" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 45,
    ],
    "OnlinePurchasingIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 46,
    ],
    "MembershipClubs" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 47,
    ],
    "ValuePriceGeneralMerchandiseBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 48,
    ],
    "ApparelWomens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 49,
    ],
    "ApparelWomensPetite" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 50,
    ],
    "ApparelWomensPlusSize" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 51,
    ],
    "ApparelWomensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 52,
    ],
    "ApparelMens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 53,
    ],
    "ApparelMensBigAndTall" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 54,
    ],
    "ApparelMensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 55,
    ],
    "ApparelChildrens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 56,
    ],
    "HealthAndBeauty" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 57,
    ],
    "BeautyCosmetics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 58,
    ],
    "Jewelry" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 59,
    ],
    "Luggage" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 60,
    ],
    "CardHolderAmericanExpressGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 61,
    ],
    "CardHolderAmericanExpressRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 62,
    ],
    "CardHolderDiscoverGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 63,
    ],
    "CardHolderDiscoverRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 64,
    ],
    "CardHolderGasolineRetailGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 65,
    ],
    "CardHolderGasolineRetailRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 66,
    ],
    "CardHolderMastercardGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 67,
    ],
    "CardHolderMastercardRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 68,
    ],
    "CardHolderVisaGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 69,
    ],
    "CardHolderVisaRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 70,
    ],
    "CardHolderBank" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 71,
    ],
    "CardHolderGasDeptRetail" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 72,
    ],
    "CardHolderTravelEntertainment" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 73,
    ],
    "CardHolderUnknownType" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 74,
    ],
    "CardHolderPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 75,
    ],
    "CardHolderUpscale" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 76,
    ],
    "CreditCardUser" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 77,
    ],
    "CreditCardNewIssue" => [
        'pattern' => '/^[B]$/', // TODO: double check this value.
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 78,
    ],
    "BankCardPresentInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 79,
    ],
    "InvestingActive" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 80,
    ],
    "InvestingPersonal" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 81,
    ],
    "InvestingRealEstate" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 82,
    ],
    "InvestingStocksBonds" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 83,
    ],
    "InvestingReadingNewsletterOrSubscriber" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 84,
    ],
    "InvestingMoneySeeker" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 85,
    ],
    "InvestingFinanceGroup" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 86,
    ],
    "InvestingForeign" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 87,
    ],
    "InvestingEstimatedResidentialPropertiesOwned" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 88,
    ],
    "DonationContributor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 89,
    ],
    "MailOrderDonor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 90,
    ],
    "CharitableDonations" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 91,
    ],
    "CharitableDonorAnimalWelfare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 92,
    ],
    "CharitableDonorArtsAndCulture" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 93,
    ],
    "CharitableDonorChildren" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 94,
    ],
    "CharitableDonorEnvironmentOrWildlife" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 95,
    ],
    "CharitableDonorEnvironmentalIssues" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 96,
    ],
    "CharitableDonorHealth" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 97,
    ],
    "CharitableDonorInternationalAid" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 98,
    ],
    "CharitableDonorPolitical" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 99,
    ],
    "CharitableDonorConservativePolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 100,
    ],
    "CharitableDonorLiberalPolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 101,
    ],
    "CharitableDonorReligious" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 102,
    ],
    "CharitableDonorVeterans" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 103,
    ],
    "CharitableDonorUnspecified" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 104,
    ],
    "CharitableDonorCommunity" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 105,
    ],
    "VeteranInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 106,
    ],
    "Parenting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 107,
    ],
    "SingleParent" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 108,
    ],
    "ApparelChildrenInfantsToddlers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 109,
    ],
    "ApparelChildrenLearningActivityToys" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 110,
    ],
    "ApparelChildrenBabyCare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 111,
    ],
    "ApparelChildrenBackToSchool" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 112,
    ],
    "ApparelChildrenGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 113,
    ],
    "YoungAdultInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 114,
    ],
    "SeniorAdultInHousehold" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 115,
    ],
    "ChildrenInterests" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 116,
    ],
    "Grandchildren" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 117,
    ],
    "ChristianFamilies" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 118,
    ],
    "Pets" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 119,
    ],
    "Equestrian" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 120,
    ],
    "CatOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 121,
    ],
    "DogOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 122,
    ],
    "OtherPetOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 123,
    ],
    "CareerImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 124,
    ],
    "WorkingWoman" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 125,
    ],
    "AfricanAmericanProfessional" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 126,
    ],
    "SOHOIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 127,
    ],
    "Career" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 128,
    ],
    "BooksMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 129,
    ],
    "Books" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 130,
    ],
    "AudioBooks" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 131,
    ],
    "ReadingGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 132,
    ],
    "ReadingReligiousOrInspirational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 133,
    ],
    "ReadingScienceFiction" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 134,
    ],
    "ReadingMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 135,
    ],
    "ReadingAudioBooks" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 136,
    ],
    "ReadingGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 137,
    ],
    "MilitaryHistory" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 138,
    ],
    "CurrentAffairsAndPolitics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 139,
    ],
    "ReligiousInspirational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 140,
    ],
    "ScienceAndSpace" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 141,
    ],
    "Magazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 142,
    ],
    "OnlineEducation" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 143,
    ],
    "Gaming" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 144,
    ],
    "HomeComputingOfficeGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 145,
    ],
    "DVDsVideos" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 146,
    ],
    "TVVideoMovieWatcher" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 147,
    ],
    "HomeComputingOffice" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 148,
    ],
    "HighEndAppliances" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 149,
    ],
    "IntendToPurchaseHDTVSatDish" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 150,
    ],
    "MusicHomeStereo" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 151,
    ],
    "MusicPlayer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 152,
    ],
    "MusicCollector" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 153,
    ],
    "MusicAvidListener" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 154,
    ],
    "MovieCollector" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 155,
    ],
    "TVCable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 156,
    ],
    "VideoGames" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 157,
    ],
    "TVSatDish" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 158,
    ],
    "Computers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 159,
    ],
    "ComputerGames" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 160,
    ],
    "ConsumerElectronics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 161,
    ],
    "MusicGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 162,
    ],
    "ElectronicsAndComputersGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 163,
    ],
    "Telecommunications" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 164,
    ],
    "Antiques" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 165,
    ],
    "Art" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 166,
    ],
    "TheaterPerformingArt" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 167,
    ],
    "ArtsGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 168,
    ],
    "MusicalInstruments" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 169,
    ],
    "CollectiblesGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 170,
    ],
    "CollectiblesStamps" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 171,
    ],
    "CollectiblesCoins" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 172,
    ],
    "CollectiblesArts" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 173,
    ],
    "CollectiblesAntiques" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 174,
    ],
    "CollectorAvid" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 175,
    ],
    "CollectiblesAntiquesGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 176,
    ],
    "CollectiblesSportsMemorabilia" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 177,
    ],
    "CollectiblesMilitary" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 178,
    ],
    "LifestyleInterestsCollectibles" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 179,
    ],
    "DoItYourselferHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 180,
    ],
    "DoItYourselferAutoWork" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 181,
    ],
    "DoItYourselferSewingKnitting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 182,
    ],
    "DoItYourselferWoodworking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 183,
    ],
    "DoItYourselferPhotography" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 184,
    ],
    "DoItYourselferAviation" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 185,
    ],
    "DoItYourselferHousePlants" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 186,
    ],
    "DoItYourselferCrafts" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 187,
    ],
    "DoItYourselferHomeAndGarden" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 188,
    ],
    "DoItYourselferGardening" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 189,
    ],
    "GeneralGardening" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 190,
    ],
    "HomeImprovementGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 191,
    ],
    "LifestylesCraftsHobbies" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 192,
    ],
    "LifestylesPhotographyVideo" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 193,
    ],
    "LifestylesSmoking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 194,
    ],
    "LifestylesDecoratingAndFurnishing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 195,
    ],
    "LifestylesHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 196,
    ],
    "LifestylesIntendToPurchaseHomeImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 197,
    ],
    "LifestylesFoodAndWine" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 198,
    ],
    "LifestylesCookingGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 199,
    ],
    "LifestylesCookingGourmet" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 200,
    ],
    "LifestylesNaturalFoods" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 201,
    ],
    "LifestylesFoodGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 202,
    ],
    "LifestylesBoardGamesAndPuzzles" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 203,
    ],
    "LifestylesCasinoGaming" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 204,
    ],
    "LifestylesSweepstakes" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 205,
    ],
    "TravelGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 206,
    ],
    "TravelGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 207,
    ],
    "TravelDomestic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 208,
    ],
    "TravelInternational" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 209,
    ],
    "TravelCruiseVacations" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 210,
    ],
    "LivingHome" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 211,
    ],
    "LivingDIY" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 212,
    ],
    "LivingSporty" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 213,
    ],
    "LivingUpscale" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 214,
    ],
    "LivingCulturalArtistic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 215,
    ],
    "LivingHighbrow" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 216,
    ],
    "LivingHighTech" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 217,
    ],
    "LivingCommon" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 218,
    ],
    "LivingProfessional" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 219,
    ],
    "LivingBroader" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 220,
    ],
    "ExerciseHealthGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 221,
    ],
    "ExerciseRunningOrJogging" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 222,
    ],
    "ExerciseWalking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 223,
    ],
    "ExerciseAerobic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 224,
    ],
    "SpectatorSportsAutoOrMotorcycleRacing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 225,
    ],
    "SpectatorSportsTVSports" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 226,
    ],
    "SpectatorSportsFootball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 227,
    ],
    "SpectatorSportsBaseball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 228,
    ],
    "SpectatorSportsBasketball" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 229,
    ],
    "SpectatorSportsHockey" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 230,
    ],
    "SpectatorSportsSoccer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 231,
    ],
    "SportsTennis" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 232,
    ],
    "SportsGolf" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 233,
    ],
    "SportsSnowSkiing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 234,
    ],
    "SportsMotorcycling" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 235,
    ],
    "SportsNascar" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 236,
    ],
    "SportsBoatingSailing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 237,
    ],
    "SportsScubaDiving" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 238,
    ],
    "SportsSportsAndLeisure" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 239,
    ],
    "SportsHunting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 240,
    ],
    "SportsFishing" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 241,
    ],
    "SportsCampingOrHiking" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 242,
    ],
    "SportsShooting" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 243,
    ],
    "SportsGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 244,
    ],
    "SportsOutdoorsGrouping" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 245,
    ],
    "HealthMedical" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 246,
    ],
    "DietingWeightLoss" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 247,
    ],
    "SelfImprovement" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 248,
    ],
    "AutoPartsAndAccessories" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 249,
    ],
    "HomeValue" => [
        'pattern' => array_keys($homeValues),
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 250,
    ],
    "HomePurchaseDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 251,
    ],
    "HomePurchasePrice" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 252,
    ],
    "HomeSalesTransactionCode" => [
        'pattern' => array_keys($homeSalesTransactionCode),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 253,
    ],
    "MortgageMostRecentAmount" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 254,
    ],
    "Mortgage2ndMostRecentAmount" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 255,
    ],
    "MortgagePurchaseAmount" => [
        'pattern' => '/^\d{2,4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 256,
    ],
    "Mortgage2ndPurchaseAmount" => [
        'pattern' => '/^\d{4,6}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 257,
    ],
    "MortgageMostRecentDate" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 258,
    ],
    "Mortgage2ndMostRecentDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 259,
    ],
    "MortgagePurchaseDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 260,
    ],
    "MortgageMostRecentLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 261,
    ],
    "Mortgage2ndMostRecentLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 262,
    ],
    "MortgagePurchaseLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 263,
    ],
    "Mortgage2ndPurchaseLoanTypeCode" => [
        'pattern' => array_keys($mortgageMostRecentLoanTypeCode),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 264,
    ],
    "MortgageMostRecentLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(3)',
        'position' => 265,
    ],
    "Mortgage2ndMostRecentLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(3)',
        'position' => 266,
    ],
    "MortgagePurchaseLenderCode" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(3)',
        'position' => 267,
    ],
    "MortgageMostRecentLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*\#]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(96)',
        'position' => 268,
    ],
    "Mortgage2ndMostRecentLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*\#]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(96)',
        'position' => 269,
    ],
    "MortgagePurchaseLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(96)',
        'position' => 270,
    ],
    "MortgageMostRecentInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 271,
    ],
    "Mortgage2ndMostRecentInterestRateType" => [
        'pattern' => '/^[A-F]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 272,
    ],
    "MortgagePurchaseInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 273,
    ],
    "Mortgage2ndPurchaseInterestRateType" => [
        'pattern' => null, // no values for this column?
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 274,
    ],
    "MortgageMostRecentInterestRate" => [
        'pattern' => '/^\d{3,5}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 275,
    ],
    "Mortgage2ndMostRecentInterestRate" => [
        'pattern' => '/^\d{3,5}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(6)',
        'position' => 276,
    ],
    "MortgagePurchaseInterestRate" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(6)',
        'position' => 277,
    ],
    "Mortgage2ndPurchaseInterestRate" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(6)',
        'position' => 278,
    ],
    "HomeYearBuilt" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(4)',
        'position' => 279,
    ],
    "HomeAirConditioning" => [
        'pattern' => array_keys($airConditioning),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 280,
    ],
    "HomePool" => [
        'pattern' => '/^[Y,N]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 281,
    ],
    "HomeFuel" => [
        'pattern' => array_keys($homeFuel),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 282,
    ],
    "HomeSewer" => [
        'pattern' => array_keys($homeSewer),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 283,
    ],
    "HomeWater" => [
        'pattern' => array_keys($homeWater),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 284,
    ],
    "LoanToValue" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 285,
    ],
    "EthnicCode" => [
        'pattern' => array_keys($ethnicCodes),
        'canBeEmpty' => true,
        'type' => 'varchar(2)',
        'position' => 286,
    ],
    "EthnicConfidence" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 287,
    ],
    "EthnicGroup" => [
        'pattern' => array_keys($ethnicGroups),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 288,
    ],
    "Language" => [
        'pattern' => array_keys($languageCodes),
        'canBeEmpty' => true,
        'type' => 'varchar(2)',
        'position' => 289,
    ],
    "Religion" => [
        'pattern' => array_keys($religion),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 290,
    ],
    "HispanicCountryCode" => [
        'pattern' => array_keys($hispanicCountryCodes),
        'canBeEmpty' => true,
        'type' => 'varchar(2)',
        'position' => 291,
    ],
    "AssimilationCode" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 292,
    ],
    "CreditRating" => [
        'pattern' => array_keys($creditRating),
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 293,
    ],
    "DNCFlag" => [
        'pattern' => '/^[F,T]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 294,
    ],
    "PermIndividualID" => [
        'pattern' => '/^\d{1,9}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 295,
    ],
    "AddressPrimary" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 296,
    ],
    "AddressPre" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 297,
    ],
    "AddressStreet" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 298,
    ],
    "AddressPost" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 299,
    ],
    "AddressSuffix" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 300,
    ],
    "AddressAbrev" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 301,
    ],
    "AddressSecy" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 302,
    ],
    "PropertyType" => [
        'pattern' => null,
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 303,
    ],
    "Males0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 304,
    ],
    "Females0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 305,
    ],
    "UnkGender0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 306,
    ],
    "Males3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 307,
    ],
    "Females3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 308,
    ],
    "UnkGender3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 309,
    ],
    "Males6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 310,
    ],
    "Females6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 311,
    ],
    "UnkGender6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 312,
    ],
    "Males11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 313,
    ],
    "Females11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 314,
    ],
    "UnkGender11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 315,
    ],
    "Males16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 316,
    ],
    "Females16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 317,
    ],
    "UnkGender16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 318,
    ],
    "Males18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 319,
    ],
    "Females18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 320,
    ],
    "UnkGender18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 321,
    ],
    "Males25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 322,
    ],
    "Females25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 323,
    ],
    "UnkGender25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 324,
    ],
    "Males35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 325,
    ],
    "Females35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 326,
    ],
    "UnkGender35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 327,
    ],
    "Males45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 328,
    ],
    "Females45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 329,
    ],
    "UnkGender45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 330,
    ],
    "Males55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 331,
    ],
    "Females55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 332,
    ],
    "UnkGender55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 333,
    ],
    "Males65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 334,
    ],
    "Females65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 335,
    ],
    "UnkGender65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 336,
    ],
    "Males75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 337,
    ],
    "Females75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 338,
    ],
    "UnkGender75" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 339,
    ],
    "People0_2" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 340,
    ],
    "People3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 341,
    ],
    "People6_10" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 342,
    ],
    "People11_15" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 343,
    ],
    "People16_17" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 344,
    ],
    "People18_24" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 345,
    ],
    "People25_34" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 346,
    ],
    "People35_44" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 347,
    ],
    "People45_54" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 348,
    ],
    "People55_64" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 349,
    ],
    "People65_74" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 350,
    ],
    "People75_and_up" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 351,
    ],
    "DPC" => [
        'pattern' => '/^\d{3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 352,
    ],
    "MSA" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
        'type' => 'varchar(8)',
        'position' => 353,
    ],
    "County" => [
        'pattern' => '/^[A-Za-z-\s]+$/',
        'canBeEmpty' => false,
        'type' => 'varchar(32)',
        'position' => 354,
    ],
    "CRRT" => [
        'pattern' => '/^[A-Z]\d{3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 355,
    ],
    "CensusTract" => [
        'pattern' => '/^\d{6}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 356,
    ],
    "CensusBlock" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 357,
    ],
    "Latitude" => [
        'pattern' => '/^\d{1,2}\.\d{6}$/i',
        'canBeEmpty' => false,
        'type' => 'varchar(16)',
        'position' => 358,
    ],
    "Longitude" => [
        'pattern' => '/^-?\d{1,3}\.\d{6}$/i',
        'canBeEmpty' => false,
        'type' => 'varchar(16)',
        'position' => 359,
    ],
    "ValueHunter" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 360,
    ],
    "OpportunitySeekers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 361,
    ],
    "NewsAndFinancial" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 362,
    ],
    "AutomotiveBuff" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 363,
    ],
    "ComputerOwner" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 364,
    ],
    "CookingEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 365,
    ],
    "DoItYourselfer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 366,
    ],
    "ExerciseEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 367,
    ],
    "OutdoorEnthusiast" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 368,
    ],
    "OutdoorSportsLover" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 369,
    ],
    "Photography" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 370,
    ],
    "Traveler" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 371,
    ],
    "CatalogResponder" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 372,
    ],
    "ReligiousMagazines" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 373,
    ],
    "MaleMerchandiseBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 374,
    ],
    "FemaleMerchandiseBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 375,
    ],
    "GardeningFarmingBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 376,
    ],
    "BookBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 377,
    ],
    "SpecialFoodsBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 378,
    ],
    "TravelHeavyBusinessTraveler" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 379,
    ],
    "HighTechLeader" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 380,
    ],
    "DonatesToEnvironmentalCauses" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 381,
    ],
    "DonatesByMail" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 382,
    ],
    "IsCharitable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 383,
    ],
    "IsGeneralContributor" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 384,
    ],
    "ChildrensProductsGeneralBackToSchool" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 385,
    ],
    "ChildrensProductsGeneralBabyCare" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 386,
    ],
    "ChildrensProductsGeneral" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 387,
    ],
    "ResidentialDeliveryIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 388,
    ],
    "CRAIncomeClassificationCode" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 389,
    ],
    "IsMortgageMostRecentLenderNameAvailable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 390,
    ],
    "MortgageMostRecentAmountCode" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 391,
    ],
    "HomeRefinanceDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 392,
    ],
    "RefinanceAmount" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 393,
    ],
    "RefinanceAmountCode" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(12)',
        'position' => 394,
    ],
    "RefinanceLenderName" => [
        'pattern' => '/^[A-Z0-9\s\-\'\/\.\&\(\)\*\#]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 395,
    ],
    "IsRefinanceLenderNameAvailable" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 396,
    ],
    "RefinanceRateType" => [
        'pattern' => array_keys($refinanceRateType),
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 397,
    ],
    "RefinanceLoanType" => [
        'pattern' => array_keys($refinanceLoanType),
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 398,
    ],
    "Age" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 399,
    ],
    "EmailAddr" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(96)',
        'position' => 400,
    ],
    "UserName" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(64)',
        'position' => 401,
    ],
    "Domain" => [
        'pattern' => '/^[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(64)',
        'position' => 402,
    ],
    "PoliticalParty" => [
        'pattern' => '/^[A-Z\s]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(64)',
        'position' => 403,
    ],
    "CongressionalDistrict" => [
        'pattern' => '/^[A-Z]+\:\d{2}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(16)',
        'position' => 404,
    ],
    "AutoYear" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 405,
    ],
    "AutoMake" => [
        'pattern' => '/^[A-Z-\s]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 406,
    ],
    "AutoModel" => [
        'pattern' => '/^[A-Z0-9-\s]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 407,
    ],
    "AutoEdition" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 408,
    ],
    "AutoTrim" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 409,
    ],
    "VIN" => [
        'pattern' => '/^[A-Z0-9]{17}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 410,
    ],
    "VFFitness" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 411,
    ],
    "VFHealthInsurance" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 412,
    ],
    "VFLifeInsurance" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 413,
    ],
    "VFTricare" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 414,
    ],
    "VFNutra" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 415,
    ],
    "VFDiabetic" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 416,
    ],
    "VFDisability" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 417,
    ],
    "OPISocialActivity" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 418,
    ],
    "OPIForums" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 419,
    ],
    "OPIBlogs" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 420,
    ],
    "OPIFileSharing" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 421,
    ],
    "OPICommerce" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 422,
    ],
    "OPIAuction" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 423,
    ],
    "OPIEntertainment" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 424,
    ],
    "OPICommunication" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 425,
    ],
    "OPIRealEstate" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 426,
    ],
    "OPIGames" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 427,
    ],
    "OPIEducation" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 428,
    ],
    "OPIApparel" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 429,
    ],
    "OPIAuto" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 430,
    ],
    "MobilePhone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(10)',
        'position' => 431,
    ],
    "MobileLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 432,
    ],
    "MobileCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 433,
    ],
    "DeviceIDPIPE" => [
        'pattern' => null, //no values for this column
        'canBeEmpty' => true,
        'type' => 'varchar(512)',
        'position' => 434,
    ],
    "FA_cc" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 435,
    ],
    "FA_PDLCtr" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 436,
    ],
    "FA_NumLoans" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 437,
    ],
    "FA_AvgLoan" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 438,
    ],
    "FA_AutoCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 439,
    ],
    "FA_AutoInsCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 440,
    ],
    "FA_LifeCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 441,
    ],
    "FA_MortCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 442,
    ],
    "FA_TaxCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 443,
    ],
    "FA_Past3MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 444,
    ],
    "FA_Past6MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 445,
    ],
    "FA_Past12MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 446,
    ],
    "FA_Past24MosCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 447,
    ],
    "FA_Over24MosCtr" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 448,
    ],
    "CityFIPS" => [
        'pattern' => '/^\d{5}|$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 449,
    ],
    "StateCountyFIPS" => [
        'pattern' => '/^\d{5}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(8)',
        'position' => 450,
    ],
    "CVGroupAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 451,
    ],
    "CVGroupPhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 452,
    ],
    "CVGroupEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 453,
    ],
    "CVGroupNamePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 454,
    ],
    "CVGroupNameEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 455,
    ],
    "CVGroupNameAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 456,
    ],
    "CVGroupMobilePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 457,
    ],
    "CVGroupMobilePhoneName" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 458,
    ],
    "PhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 459,
    ],
    "PhoneCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 460,
    ],
    "MobilePhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1)',
        'position' => 461,
    ],
    "MobilePhoneCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 462,
    ],
    "PROP_OwnerPropertyDescription" => [
        'pattern' => '/^[A-Z]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 463,
    ],
    "PROP_PropertyUseGroup" => [
        'pattern' => '/^[A-Z\s\/]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 464,
    ],
    "PROP_AreaBuilding" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 465,
    ],
    "PROP_AreaFirstFloor" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
        'type' => 'int(11)',
        'position' => 466,
    ],
    "PROP_AreaSecondFloor" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 467,
    ],
    "PROP_AreaLotAcres" => [
        'pattern' => '/^\d+(\.\d+)?$/',
        'canBeEmpty' => true,
        'type' => 'float',
        'position' => 468,
    ],
    "PROP_AreaLotSF" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 469,
    ],
    "PROP_BathCount" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 470,
    ],
    "PROP_BathPartialCount" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 471,
    ],
    "PROP_BedroomsCount" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 472,
    ],
    "PROP_StoriesCount" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 473,
    ],
    "PROP_UnitsCount" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 474,
    ],
    "PROP_YearBuilt" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 475,
    ],
    "PROP_TaxAssessedValueTotal" => [
        'pattern' => '/^\d{1,8}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 476,
    ],
    "PROP_TaxMarketValueTotal" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 477,
    ],
    "PROP_Pool" => [
        'pattern' => '/^\d{1,3}$/',
        'canBeEmpty' => true,
        'type' => 'varchar(11)',
        'position' => 478,
    ],
    "PROP_REC_GranteeInvestorFlag" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 479,
    ],
    "PROP_REC_PropertyUseGroup" => [
        'pattern' => '/^[A-Z\s\/]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(32)',
        'position' => 480,
    ],
    "PROP_REC_InvestorFlagTrueCtr" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => true,
        'type' => 'int(11)',
        'position' => 481,
    ],
    "PROP_REC_InvestorFlagTruePctg" => [
        'pattern' => '/^\d(\.\d+)?$/',
        'canBeEmpty' => true,
        'type' => 'float',
        'position' => 482,
    ],
    "AUDEX_DevIDPIPE" => [
        'pattern' => '/^[A-Z0-9-|]+$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1024)',
        'position' => 483,
    ],
    "AUDEX_EmailAddrPIPE" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}|$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1024)',
        'position' => 484,
    ],
    "AUDEX_PhonePIPE" => [
        'pattern' => '/^\d{8,10}|$/',
        'canBeEmpty' => true,
        'type' => 'varchar(1024)',
        'position' => 485,
    ],
];
