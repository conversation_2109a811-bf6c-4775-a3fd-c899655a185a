<?php

namespace Tests\Support\Page;

class EmailValidation {

    // Authorize Deduction Modal
    public static $includeAltEmailsLabel = "//label[@for='vs-ev-wizard-alts-yes']";
    public static $authorizeDeducationLabel = "//label[@for='vs-ev-debit-agree']";
    public static $orderListButton = "//button[text()='Order List']";
    public static $authorizeDeducationLabelText = 'By checking the box, you authorize the deduction of Match Credits listed above from your subscription.';
    public static $authorizeDeducationWithAltsLabelText = 'By checking the box, you authorize the deduction of Match Credits listed above for email validation, plus 1 additional credit for each valid alternate email returned.';

    // Report Value Elements
    public static $emailsValidatedValue = "//div[@id='vs-ev-emails-validated-stat-block']//div[2]";
    public static $emailsValidValue = "//div[@id='vs-ev-valid-emails-stat-block']//div[2]";
    public static $catchAllEmailsValue = "//div[@id='vs-ev-catch-all-emails-stat-block']//div[2]";
    public static $unsafeEmailsValue = "//div[@id='vs-ev-unsafe-emails-stat-block']//div[2]";
    public static $replacedEmailsValue = "//div[@id='vs-ev-replaced-emails-stat-block']//div[2]";

    public static $emailValidationFinished = "//h3[text()='Email Status Breakdown']";

    // Getters
    public static $reportValues = null;
    public static $reportValuesWithAlts = null;

    // These are the values we expect given the Consumer.csv file
    public static function getReportValues()
    {
        if (self::$reportValues == null) {
            self::$reportValues = [
                self::$emailsValidatedValue => '99 Emails',
                self::$emailsValidValue     => '71 Emails (71.72%)',
                self::$catchAllEmailsValue  => '5 Emails (5.05%)',
                self::$unsafeEmailsValue    => '23 Emails (23.23%)',
            ];
        }
        return self::$reportValues;
    }

    public static function getReportValuesWithAlts()
    {
        if (self::$reportValuesWithAlts == null) {
            self::$reportValuesWithAlts = [
                self::$emailsValidatedValue => '99 Emails',
                self::$emailsValidValue     => '71 Emails (71.72%)',
                self::$catchAllEmailsValue  => '5 Emails (5.05%)',
                self::$unsafeEmailsValue    => '23 Emails (23.23%)',
                self::$replacedEmailsValue  => '11 Emails',
            ];
        }
        return self::$reportValuesWithAlts;
    }
}