<?php

use Tests\Support\AcceptanceTester;
use Tests\Support\Classes\ListGenCustomLimits;
use Tests\Support\Classes\ListGenGeoFilters;
use Tests\Support\Classes\ListGenDemoFilters;
use Tests\Support\Classes\ListGenAttrConfig;
use Tests\Support\Classes\ListGenContact;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;

use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;
use Codeception\Attribute\Skip;

use Tests\Support\Enums\MatchType;
use Tests\Support\Enums\ContactType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\DemographicType;

// TODO:
// Geofilters: County, Radius
// Actions Bar tests?
// BLOCKED: Fin/Household: mortage rate type/2nd mortagage rate type, & refi rate type: These values overlap with each other.

class ListGen2Cest {

    private static $listName = null;
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'ListGenB2C-' . time();
        }
        return self::$projectName;
    }

    private static function getListName()
    {
        if (self::$listName == null) {
            self::$listName = 'listgen-automation-' . time();
        }
        return self::$listName;
    }

    private static function getListNameWithAO()
    {
        if (self::$listName == null) {
            self::$listName = 'listgen-automation-oa-' . time();
        }
        return self::$listName;
    }

    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->logout();
    }


    #[Group('sanity')]
    #[Group('listgen')]
    public function createList(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::NewProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
                new ListGenContact(ContactType::Email),
            ],
            new ListGenCustomLimits(MatchType::Indiv, 500),
            new ListGenGeoFilters(['Seattle', 'Portland, OR']),
            new ListGenDemoFilters(
                [['d_gender', 'Male']],
                [['d_ownrent', 'Owner']],
                ['Exercise'],
                [
                    ['DEMOCRAT'],
                ]
            ),
            new ListGenAttrConfig(false, [
                DemographicType::Demographic,
                DemographicType::Lifestyle,
                DemographicType::Household,
                DemographicType::Political,
            ])
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('sanity')]
    #[Group('listgen')]
    public function createListWithOA(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListNameWithAO(),
            self::getProjectName(),
            ProjectType::NewProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
                new ListGenContact(ContactType::Email),
                new ListGenContact(ContactType::OnlineAudience),
            ],
            new ListGenCustomLimits(MatchType::Indiv, 500),
            new ListGenGeoFilters(['Seattle', 'Portland, OR']),
            new ListGenDemoFilters(
                [['d_gender', 'Male']],
                [['d_ownrent', 'Owner']],
                ['Exercise'],
                [
                    ['DEMOCRAT'],
                ]
            ),
            new ListGenAttrConfig(false, [
                DemographicType::Demographic,
                DemographicType::Lifestyle,
                DemographicType::Household,
                DemographicType::Political,
            ])
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('sanity')]
    #[Group('listgen')]
    #[Depends('createList')]
    public function exportList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            self::getListName() . '.csv'
        );

        $I->wait(1);
        $I->waitForElementAndClick(VisualizationPage::$primaryResultsBtn);
        $I->waitForElementAndClick(VisualizationPage::$modalExportBtn);
        $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);
        $I->wait(2);
    }

    #[Group('sanity')]
    #[Group('listgen')]
    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $headers = ["First Name","Last Name","Postal Address","City","State","Zip","County","Email Address","Phone","Line Type","DOB","Age","Age Range","Gender","Ethnic Group","Religion","Education Level","Occupation","Language","Marital Status","Working Woman in Household","Senior in Household","Single Parent","Presence of Children","Number of Children","Young Adult in Household","Small Office or Home Office","Online Purchasing Indicator","Online Education","Magazines","Reading","Current Affairs and Politics","Mail Order Buyer","Dieting and Weight Loss","Travel","Music","Consumer Electronics","Arts","Antiques","Home Improvement","Gardening","Cooking","Exercise","Sports","Outdoors","Womens Apparel","Mens Apparel","Pets","Investing","Health and Beauty","Decorating and Furnishing","Home Own or Rent","Household Income","Estimated Net Worth","Home Year Built","Home Purchase Date","Home Purchase Price","Dwelling Type","Home Value","Length of Residence","Credit Card Holder","Bank","Number of Credit Lines","Upscale Card Holder","Credit Rating","Mortgage Purchase Amount","Mortgage Purchase Loan Type","Mortgage Purchase Interest Rate Type","Mortgage Purchase Date","2nd Most Recent Mortgage Amount","2nd Most Recent Mortgage Loan Type","2nd Most Recent Mortgage Interest Rate Type","2nd Most Recent Mortgage Date","Loan to Value","Refinance Date","Refinance Amount","Refinance Loan Type","Refinance Rate Type","Home Pool","Auto Year","Auto Make","Auto Model","Party Affiliation","Donor Environmental","Donor Animal Welfare","Donor Arts and Culture","Donor Childrens Causes","Donor Environmental or Wildlife","Donor Health","Donor International Aid","Donor Political","Donor Conservative Politics","Donor Liberal Politics","Donor Religious","Donor Veterans","Donor Unspecified","Donor Community"];
        $listName = self::getListName() . '.csv';

        $I->openFile($I->getDownloadDir() . $listName);

        foreach ($headers as $h) {
            $I->seeInThisFile($h);
        }
    }

    /*************************************************** START FULL FILTER COVERAGE ************************************************************************/

    #[Group('listgen')]
    public function demographicFilters1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(['Seattle']),
            new ListGenDemoFilters([
                ['d_education', 'Completed College'],
                ['d_gender', 'Male'],
                ['d_occupation', 'Professional/Technical'],
                ['d_religion', 'Catholic'],
                ['d_language', 'English'],
                ['d_ethnicgroup', 'Western European'],
            ]),
            null,
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function demographicFilters2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, null, ['98125']),
            new ListGenDemoFilters([
                ['d_maritalstatus', 'Married'],
                ['d_maritalstatus', 'Married (Inferred)'],
                ['d_age', [6, 35]], // selects 24-64
                ['d_num_children', [1, 5]] // selects 1-3
            ]),
            null,
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }


    #[Group('listgen')]
    public function householdFilters1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(['Seattle']),
            new ListGenDemoFilters(null, [
                ['d_auto_makemodel', 'HONDA CIVIC'],
                ['d_auto_makemodel', 'HONDA ACCORD'],
                ['d_auto_year', [15, 0]], //selects 1995-2024
                ['d_creditlines', [1, 0]], //selects 2-9
                ['d_creditrating', [4, 0]], //selects 650-800
                ['d_networth', [1, 1]], //selects $1-$500k
                ['d_esthhldincome', [1, 1]], //selects $10k-$250k
                // TODO:
                // ['d_mortgage_purchase_date', []]
                // ['d_mortgage_purchase_loan_type_code', []]
                // ['d_mortgage_rate', []]
                // ['d_mortgage_rate_type', []]
                // ['d_mortgage_amount', []]
                // ['d_refi_loan_type', 'Conventional'],
                // ['d_refi_rate_type', 'Fixed'], //known issue selecting values
                // ['d_refi_amount', []],
                // ['d_refi_date', []],
            ]),
            null,
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    // TODO: takes too long
    #[Skip()]
    public function householdFilters2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, ['Washington']),
            new ListGenDemoFilters(null, [
                ['d_dwellingtype', 'Single Family Dwelling Unit'],
                ['d_ownrent', 'Owner'],
                ['d_home_purchase_date', [20, 0]], //selects 1980-2024
                ['d_home_purchase_price', [5, 0]], //selects $250k->$2M
                ['d_home_value', [8, 0]],          //selects $200K->$1M
                ['d_lor', [1, 0]],                 //selects 1-15+
                ['d_home_year', [40, 0]],          //selects 1980-2024
                ['d_loantovalue', [9,10]],         //selects 10%-90%
            ]),
            null,
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    // TODO: click intercepted
    #[Skip()]
    #[Group('listgen')]
    public function householdFilters3(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, ['Washington', 'Oregon']),
            new ListGenDemoFilters(null, [
                ['d_mortgage2_loan_type', 'CONVENTIONAL'],
                ['d_mortgage2_date', [5, 0]], //selects 1995-2024
                ['d_mortgage2_rate', [3, 20]], //selects 1%-10%
                ['d_mortgage2_amount', [3, 0]], //selects $150k - >$1mil
                // ['d_mortgage2_rate_type', 'Fixed'], //known issue selecting this
            ]),
            null,
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    // TODO: Too slow. Times out running the list
    #[Skip()]
    #[Group('listgen')]
    public function lifestyleFilters1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, ['Washington']),
            new ListGenDemoFilters(null, null, [
                'Antiques',
                'Arts',
                'Consumer Electronics',
                'Cooking',
                'Current Affairs and Politics',
            ]),
            new ListGenAttrConfig(true),
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function lifestyleFilters2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, null, ['98006', '98007', '98008']),
            new ListGenDemoFilters(null, null, [
                'Decorating and Furnishing',
                'Dieting & Weight Loss',
                'Exercise',
                'Fishing',
            ]),
            new ListGenAttrConfig(true),
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function lifestyleFilters3(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, ['Washington', 'Oregon']),
            new ListGenDemoFilters(null, null, [
                'Gardening',
                'Golf',
                'Health and Beauty',
                'Home Improvement',
                'Hunting',
                'Investing',
                'Magazines',
                'Mail Order Buyer',
                'Men\'s Apparel',
                'Motorcycles',
                'Music',
                'Online Purchasing',
            ]),
            new ListGenAttrConfig(true),
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function lifestyleFilters4(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, null, ['98007','98008','98125']),
            new ListGenDemoFilters(null, null, [
                'Outdoors',
                'Pets',
                'Reading',
                'Shooting',
                'Sports',
                'Travel',
                'Women\'s Apparel',
            ]),
            new ListGenAttrConfig(true),
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function politicalFilters1(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, null, ['99301', '98052', '98012']),
            new ListGenDemoFilters(null, null, null, [
                ['DEMOCRAT'],
                ['LIBERTARIAN'],
                ['INDEPENDENT'],
                ['NO AFFILIATION'],
                ['GREEN'],
                ['Donor Animal Welfare'],
                ['Donor Community'],
                ['Donor Environmental or Wildlife'],
                ['Donor Liberal'],
                ['Donor Unspecified'],
                ['Donor Arts and Culture'],
            ]),
            new ListGenAttrConfig(true)
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }

    #[Group('listgen')]
    public function politicalFilters2(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$listGenCard);
        $I->doB2cListGenFlow(
            self::getListName(),
            self::getProjectName(),
            ProjectType::ExistingProject,
            [
                new ListGenContact(ContactType::Address),
                new ListGenContact(ContactType::Phone),
            ],
            null,
            new ListGenGeoFilters(null, null, ['Idaho']),
            new ListGenDemoFilters(null, null, null, [
                ['REPUBLICAN'],
                ['LIBERTARIAN'],
                ['INDEPENDENT'],
                ['NO AFFILIATION'],
                ['REFORM'],
                ['Donor Conservative'],
                ['Donor Health'],
                ['Donor Political'],
                ['Donor Veterans'],
                ['Donor Children\'s Causes'],
                ['Donor Environmental'],
                ['Donor International Aid'],
                ['Donor Religious'],
            ]),
            new ListGenAttrConfig(true)
        );
        $I->waitForElement(VisualizationPage::$b2cListGenFinished, 1800);
    }
}