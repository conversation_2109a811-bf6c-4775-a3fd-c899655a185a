<?php

namespace Tests\Acceptance\b2c;

use Tests\Support\AcceptanceTester;
use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\JobType;
use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Codeception\Attribute\Depends;
use Codeception\Attribute\Group;

class ContactAppendCest
{
    private static $listName = 'consumer-no-addr.csv';
    private static $projectName = null;

    private static function getProjectName()
    {
        if (self::$projectName == null) {
            self::$projectName = 'ContactAppend' . time();
        }
        return self::$projectName;
    }


    public function _before(AcceptanceTester $I)
    {
        $I->login($I->getEmail(), $I->getPassword());
        $I->dismissAppVersionToast($I);
    }

    public function _after(AcceptanceTester $I)
    {
        $I->wait(1);
        $I->logout();
    }


    #[Group('sanity')]
    public function createContactAppend(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->waitForText('Create a Contact Append');

        $I->doB2cContactAppend(
            ImportListType::NewList,
            self::$listName,
            self::getProjectName(),
            [AttributeSelectPage::$addressCheckBoxLabel],
            ProjectType::NewProject,
        );
        $I->waitForElement(VisualizationPage::$b2cContactAppendFinishedElem, 1800);
    }


    #[Depends('createContactAppend')]
    public function createContactAppendFromExistingList(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->waitForText('Create a Contact Append');

        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$listName,
            self::getProjectName(),
            [AttributeSelectPage::$addressCheckBoxLabel],
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cContactAppendFinishedElem, 1800);
    }


    #[Group('hubspot')]
    #[Depends('createContactAppend')]
    public function createContactAppendFromHubspot(AcceptanceTester $I)
    {
        $I->waitForElementAndClick(DashboardPage::$contactAppendCard);
        $I->waitForText('Create a Contact Append');

        $I->doB2cContactAppend(
            ImportListType::HubspotList,
            $I::HUBSPOT_LIST_NAME,
            self::getProjectName(),
            [AttributeSelectPage::$addressCheckBoxLabel],
            ProjectType::ExistingProject,
        );
        $I->waitForElement(VisualizationPage::$b2cContactAppendFinishedElem, 1800);
    }


    #[Depends('createContactAppend')]
    public function createContactAppendFromListActions(AcceptanceTester $I)
    {
        $projectName = self::getProjectName();

        $I->doB2CListAction(
            $projectName,
            self::$listName,
            JobType::ContactAppend,
        );
        $I->doB2cContactAppend(
            ImportListType::ExistingList,
            self::$listName,
            $projectName,
            [AttributeSelectPage::$addressCheckBoxLabel],
            ProjectType::PreSelectedProject,
        );
        $I->waitForElement(VisualizationPage::$b2cContactAppendFinishedElem, 1800);
    }


    #[Depends('createContactAppend')]
    #[Group('sanity')]
    public function exportList(AcceptanceTester $I)
    {
        $I->openListFromProject(
            self::getProjectName(),
            basename(self::$listName, '.csv') . '-contact-append.csv',
        );

        $I->waitForElementAndClick(VisualizationPage::$primaryResultsBtn);
        $I->waitForElementAndClick(VisualizationPage::$modalExportBtn);
        $I->waitForElementAndClick(VisualizationPage::$acceptTOSBtn);

        $I->wait(3);
        $I->waitForText("This file can be uploaded to Marketing Star for email and SMS campaigns.");
        $I->waitForElementAndClick(VisualizationPage::$doneBtn);
    }


    #[Depends('exportList')]
    public function exportedListHasData(AcceptanceTester $I)
    {
        $listName = basename(self::$listName, ".csv") . '-contact-append.csv';

        $I->openFile($I->getDownloadDir() . $listName);
        $I->seeInThisFile('Sender_Email,Phone,City,State,Zip,FirstName,LastName');
    }
}
